<template>
  <header class="page-header">
    <div class="header-top">
      <div class="container">
        <div class="welcome-info">
          <i class="fas fa-home"></i>
          <span>歡迎來到數字起源 (香港) 有限公司！</span>
        </div>
        <div class="header-actions">
          <button class="lang-btn" @click="toggleLanguage">{{ currentLang }}</button>
          <div class="search-box">
            <input
              type="text"
              v-model="searchQuery"
              placeholder="搜索文章、頁面"
              @keyup.enter="performSearch"
            />
            <button class="search-btn" @click="performSearch">
              <img src="/uploads/sites/1012/2022/12/5981d2b1347e3df2eb51c919f1b7e57b.png" alt="搜索" />
            </button>
          </div>
          <button class="login-btn" @click="handleLogin">登錄/註冊</button>
        </div>
      </div>
    </div>
    
    <!-- 主导航 -->
    <nav class="main-nav">
      <div class="container">
        <ul class="nav-menu">
          <li class="nav-item current">
            <a href="/">首頁</a>
          </li>
          <li class="nav-item has-dropdown">
            <a href="/solutions">解決方案</a>
            <div class="dropdown-menu">
              <div class="dropdown-content">
                <div class="solution-category">
                  <h4>行業解決方案</h4>
                  <ul>
                    <li><a href="/smart-tourism">智慧文旅</a></li>
                    <li><a href="/smart-city">城市服務</a></li>
                    <li><a href="/smart-ecology">智慧生態</a></li>
                  </ul>
                </div>
                <div class="solution-category">
                  <h4>通用解決方案</h4>
                  <ul>
                    <li><a href="/ai-solutions">人工智能</a></li>
                    <li><a href="/data-solutions">數據服務</a></li>
                  </ul>
                </div>
              </div>
            </div>
          </li>
          <li class="nav-item">
            <a href="/insights">行業洞察</a>
          </li>
          <li class="nav-item">
            <a href="/experience">體驗中心</a>
          </li>
          <li class="nav-item">
            <a href="/contact">聯繫我們</a>
          </li>
          <li class="nav-item">
            <a href="/careers">招賢納士</a>
          </li>
        </ul>
      </div>
    </nav>
  </header>
</template>

<script>
export default {
  name: 'AppHeader',
  data() {
    return {
      currentLang: 'EN',
      searchQuery: ''
    }
  },
  methods: {
    toggleLanguage() {
      this.currentLang = this.currentLang === 'EN' ? '中文' : 'EN'
      // 这里可以添加语言切换逻辑
      console.log('切换语言到:', this.currentLang)
    },
    performSearch() {
      if (this.searchQuery.trim()) {
        console.log('搜索:', this.searchQuery)
        // 这里可以添加搜索逻辑
        this.$router.push(`/search?q=${encodeURIComponent(this.searchQuery)}`)
      }
    },
    handleLogin() {
      console.log('跳转到登录页面')
      // 这里可以添加登录逻辑
      this.$router.push('/login')
    }
  }
}
</script>

<style scoped>
/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 页面头部样式 */
.page-header {
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-top {
  background: #f8f9fa;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.header-top .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #0064d8;
  font-size: 14px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.lang-btn {
  padding: 8px 12px;
  border: 1px solid #eee;
  background: #fff;
  color: #333;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.lang-btn:hover {
  background: #0064d8;
  color: #fff;
  border-color: #0064d8;
}

.search-box {
  display: flex;
  align-items: center;
  border: 1px solid #eee;
  border-radius: 4px;
  overflow: hidden;
}

.search-box input {
  padding: 8px 12px;
  border: none;
  outline: none;
  width: 200px;
  font-size: 14px;
}

.search-btn {
  padding: 8px 12px;
  background: #0081f9;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn img {
  width: 16px;
  height: 16px;
}

.login-btn {
  padding: 8px 16px;
  background: #0081f9;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.login-btn:hover {
  background: #0064d8;
}

/* 主导航样式 */
.main-nav {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  justify-content: flex-end;
}

.nav-item {
  position: relative;
}

.nav-item > a {
  display: block;
  padding: 15px 20px;
  color: #fff;
  text-decoration: none;
  font-size: 16px;
  transition: all 0.3s;
}

.nav-item:hover > a,
.nav-item.current > a {
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
}

.nav-item.has-dropdown:hover .dropdown-menu {
  display: block;
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background: rgba(229, 239, 251, 1);
  min-width: 600px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.dropdown-content {
  display: flex;
  padding: 30px;
}

.solution-category {
  flex: 1;
  margin-right: 30px;
}

.solution-category h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 16px;
}

.solution-category ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.solution-category li {
  margin-bottom: 10px;
}

.solution-category a {
  color: #333;
  text-decoration: none;
  padding: 8px 0;
  display: block;
  transition: all 0.3s;
}

.solution-category a:hover {
  color: #0064d8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .search-box input {
    width: 150px;
  }
  
  .nav-menu {
    flex-direction: column;
  }
}
</style>
